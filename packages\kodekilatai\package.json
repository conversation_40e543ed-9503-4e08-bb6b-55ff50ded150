{"name": "@kodekilat/kodekilatai", "version": "1.0.0", "description": "KodeKilat Studio - AI Package", "main": "dist/index.js", "types": "dist/index.d.ts", "private": true, "scripts": {"build": "tsc", "dev": "tsc --watch", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "openai": "^4.20.1", "@anthropic-ai/sdk": "^0.9.1", "@huggingface/inference": "^2.6.4", "@kodekilat/types": "workspace:*", "@kodekilat/db": "workspace:*"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/node": "^20.10.0", "typescript": "^5.3.2"}}