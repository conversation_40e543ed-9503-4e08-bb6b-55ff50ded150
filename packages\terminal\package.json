{"name": "@kodekilat/terminal", "version": "1.0.0", "description": "KodeKilat Studio - Terminal Package", "main": "dist/index.js", "types": "dist/index.d.ts", "private": true, "scripts": {"build": "tsc", "dev": "tsc --watch", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-web-links": "^0.9.0", "node-pty": "^1.0.0", "@kodekilat/types": "workspace:*", "@kodekilat/db": "workspace:*"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/node": "^20.10.0", "typescript": "^5.3.2"}}