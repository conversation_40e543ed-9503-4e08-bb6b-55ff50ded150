import { db } from "./index";
import { Database } from "bun:sqlite";

// User operations
export const userOperations = {
  create: (username: string, email: string, passwordHash: string) => {
    const stmt = db.prepare(`
      INSERT INTO users (username, email, password_hash)
      VALUES (?, ?, ?)
    `);
    return stmt.run(username, email, passwordHash);
  },

  findByUsername: (username: string) => {
    const stmt = db.prepare("SELECT * FROM users WHERE username = ?");
    return stmt.get(username);
  },

  findById: (id: number) => {
    const stmt = db.prepare("SELECT * FROM users WHERE id = ?");
    return stmt.get(id);
  },

  updateSettings: (id: number, settings: object) => {
    const stmt = db.prepare(`
      UPDATE users SET settings = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    return stmt.run(JSON.stringify(settings), id);
  }
};

// Session operations
export const sessionOperations = {
  create: (id: string, userId: number, expiresAt: Date) => {
    const stmt = db.prepare(`
      INSERT INTO sessions (id, user_id, expires_at)
      VALUES (?, ?, ?)
    `);
    return stmt.run(id, userId, expiresAt.toISOString());
  },

  findById: (id: string) => {
    const stmt = db.prepare("SELECT * FROM sessions WHERE id = ?");
    return stmt.get(id);
  },

  delete: (id: string) => {
    const stmt = db.prepare("DELETE FROM sessions WHERE id = ?");
    return stmt.run(id);
  },

  cleanup: () => {
    const stmt = db.prepare("DELETE FROM sessions WHERE expires_at < CURRENT_TIMESTAMP");
    return stmt.run();
  }
};

// Project operations
export const projectOperations = {
  create: (name: string, path: string, framework?: string) => {
    const stmt = db.prepare(`
      INSERT INTO projects (name, path, framework)
      VALUES (?, ?, ?)
    `);
    return stmt.run(name, path, framework || null);
  },

  findByPath: (path: string) => {
    const stmt = db.prepare("SELECT * FROM projects WHERE path = ?");
    return stmt.get(path);
  },

  findById: (id: number) => {
    const stmt = db.prepare("SELECT * FROM projects WHERE id = ?");
    return stmt.get(id);
  },

  getAll: () => {
    const stmt = db.prepare("SELECT * FROM projects ORDER BY last_opened DESC");
    return stmt.all();
  },

  updateLastOpened: (id: number) => {
    const stmt = db.prepare(`
      UPDATE projects SET last_opened = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    return stmt.run(id);
  },

  updateRunningState: (id: number, isRunning: boolean, port?: number) => {
    const stmt = db.prepare(`
      UPDATE projects SET is_running = ?, port = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    return stmt.run(isRunning, port || null, id);
  },

  updateSettings: (id: number, settings: object) => {
    const stmt = db.prepare(`
      UPDATE projects SET settings = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    return stmt.run(JSON.stringify(settings), id);
  }
};

// Editor state operations
export const editorOperations = {
  saveState: (projectId: number, filePath: string, content: string, cursorPosition: object, scrollPosition: number) => {
    const stmt = db.prepare(`
      INSERT OR REPLACE INTO editor_state 
      (project_id, file_path, content, cursor_position, scroll_position, is_dirty, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    `);
    return stmt.run(projectId, filePath, content, JSON.stringify(cursorPosition), scrollPosition, true);
  },

  getState: (projectId: number, filePath: string) => {
    const stmt = db.prepare(`
      SELECT * FROM editor_state 
      WHERE project_id = ? AND file_path = ?
    `);
    return stmt.get(projectId, filePath);
  },

  markSaved: (projectId: number, filePath: string) => {
    const stmt = db.prepare(`
      UPDATE editor_state 
      SET is_dirty = FALSE, last_saved = CURRENT_TIMESTAMP
      WHERE project_id = ? AND file_path = ?
    `);
    return stmt.run(projectId, filePath);
  },

  getOpenFiles: (projectId: number) => {
    const stmt = db.prepare(`
      SELECT file_path, is_dirty, last_saved FROM editor_state 
      WHERE project_id = ?
      ORDER BY updated_at DESC
    `);
    return stmt.all(projectId);
  }
};

// AI operations
export const aiOperations = {
  createSession: (id: string, projectId: number | null, title: string, provider: string, model: string, mode: string = 'chat') => {
    const stmt = db.prepare(`
      INSERT INTO ai_sessions (id, project_id, title, provider, model, mode)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    return stmt.run(id, projectId, title, provider, model, mode);
  },

  addMessage: (sessionId: string, role: string, content: string, metadata: object = {}) => {
    const stmt = db.prepare(`
      INSERT INTO ai_messages (session_id, role, content, metadata)
      VALUES (?, ?, ?, ?)
    `);
    return stmt.run(sessionId, role, content, JSON.stringify(metadata));
  },

  getSession: (id: string) => {
    const stmt = db.prepare("SELECT * FROM ai_sessions WHERE id = ?");
    return stmt.get(id);
  },

  getMessages: (sessionId: string) => {
    const stmt = db.prepare(`
      SELECT * FROM ai_messages 
      WHERE session_id = ? 
      ORDER BY created_at ASC
    `);
    return stmt.all(sessionId);
  },

  getSessions: (projectId?: number) => {
    if (projectId) {
      const stmt = db.prepare(`
        SELECT * FROM ai_sessions 
        WHERE project_id = ? 
        ORDER BY updated_at DESC
      `);
      return stmt.all(projectId);
    } else {
      const stmt = db.prepare(`
        SELECT * FROM ai_sessions 
        ORDER BY updated_at DESC
      `);
      return stmt.all();
    }
  },

  updateSessionTitle: (id: string, title: string) => {
    const stmt = db.prepare(`
      UPDATE ai_sessions 
      SET title = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    return stmt.run(title, id);
  }
};

// Terminal operations
export const terminalOperations = {
  createSession: (id: string, projectId: number | null, name: string, cwd: string, shell: string) => {
    const stmt = db.prepare(`
      INSERT INTO terminal_sessions (id, project_id, name, cwd, shell)
      VALUES (?, ?, ?, ?, ?)
    `);
    return stmt.run(id, projectId, name, cwd, shell);
  },

  logOutput: (sessionId: string, type: string, content: string) => {
    const stmt = db.prepare(`
      INSERT INTO terminal_logs (session_id, type, content)
      VALUES (?, ?, ?)
    `);
    return stmt.run(sessionId, type, content);
  },

  getSession: (id: string) => {
    const stmt = db.prepare("SELECT * FROM terminal_sessions WHERE id = ?");
    return stmt.get(id);
  },

  getLogs: (sessionId: string, limit: number = 1000) => {
    const stmt = db.prepare(`
      SELECT * FROM terminal_logs 
      WHERE session_id = ? 
      ORDER BY timestamp DESC 
      LIMIT ?
    `);
    return stmt.all(sessionId, limit);
  },

  closeSession: (id: string) => {
    const stmt = db.prepare(`
      UPDATE terminal_sessions 
      SET is_active = FALSE 
      WHERE id = ?
    `);
    return stmt.run(id);
  }
};

// Git operations
export const gitOperations = {
  updateState: (projectId: number, branch: string, status: object, lastCommit?: string, remoteUrl?: string, isDirty: boolean = false) => {
    const stmt = db.prepare(`
      INSERT OR REPLACE INTO git_state 
      (project_id, branch, status, last_commit, remote_url, is_dirty, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    `);
    return stmt.run(projectId, branch, JSON.stringify(status), lastCommit, remoteUrl, isDirty);
  },

  getState: (projectId: number) => {
    const stmt = db.prepare("SELECT * FROM git_state WHERE project_id = ?");
    return stmt.get(projectId);
  },

  logCommand: (projectId: number, command: string, output?: string, error?: string, exitCode: number = 0) => {
    const stmt = db.prepare(`
      INSERT INTO git_logs (project_id, command, output, error, exit_code)
      VALUES (?, ?, ?, ?, ?)
    `);
    return stmt.run(projectId, command, output, error, exitCode);
  },

  getCommandHistory: (projectId: number, limit: number = 100) => {
    const stmt = db.prepare(`
      SELECT * FROM git_logs 
      WHERE project_id = ? 
      ORDER BY timestamp DESC 
      LIMIT ?
    `);
    return stmt.all(projectId, limit);
  }
};

// Extension operations
export const extensionOperations = {
  install: (id: string, name: string, version: string, author: string, description: string, installPath: string) => {
    const stmt = db.prepare(`
      INSERT OR REPLACE INTO extensions 
      (id, name, version, author, description, install_path)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    return stmt.run(id, name, version, author, description, installPath);
  },

  getAll: () => {
    const stmt = db.prepare("SELECT * FROM extensions ORDER BY name ASC");
    return stmt.all();
  },

  getEnabled: () => {
    const stmt = db.prepare("SELECT * FROM extensions WHERE is_enabled = TRUE ORDER BY name ASC");
    return stmt.all();
  },

  toggle: (id: string, enabled: boolean) => {
    const stmt = db.prepare(`
      UPDATE extensions 
      SET is_enabled = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    return stmt.run(enabled, id);
  },

  uninstall: (id: string) => {
    const stmt = db.prepare("DELETE FROM extensions WHERE id = ?");
    return stmt.run(id);
  },

  updateSettings: (id: string, settings: object) => {
    const stmt = db.prepare(`
      UPDATE extensions 
      SET settings = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    return stmt.run(JSON.stringify(settings), id);
  }
};

// Utility functions
export const dbUtils = {
  vacuum: () => {
    db.exec("VACUUM");
  },

  backup: (backupPath: string) => {
    const backup = new Database(backupPath);
    db.backup(backup);
    backup.close();
  },

  getStats: () => {
    const tables = [
      'users', 'sessions', 'projects', 'editor_state', 'ai_sessions', 
      'ai_messages', 'terminal_sessions', 'terminal_logs', 'git_state', 
      'git_logs', 'extensions', 'designer_sessions', 'preview_state', 'fs_cache'
    ];
    
    const stats: Record<string, number> = {};
    
    for (const table of tables) {
      const stmt = db.prepare(`SELECT COUNT(*) as count FROM ${table}`);
      const result = stmt.get() as { count: number };
      stats[table] = result.count;
    }
    
    return stats;
  }
};
