# KodeKilat Studio ⚡

> IDE Nusantara Modern - Cepat, Lokal, Tanpa Internet, Tidak Ribet

## 🚀 Tentang KodeKilat Studio

KodeKilat Studio adalah IDE desktop native yang menggabungkan kekuatan Windsurf + VSCode + Onlook dalam satu aplikasi yang sepenuhnya offline-first. Dirancang khusus untuk developer Indonesia dengan filosofi "Cepat ⚡, Lokal, Tanpa Internet, Tidak Ribet".

## ✨ Fitur Utama

- 🎨 **UI Designer**: Drag-and-drop UI builder dengan live preview
- 🤖 **KodeKilat AI**: Multi-provider AI assistant (OpenAI, Claude, HuggingFace, Local)
- 📝 **Monaco Editor**: Multi-tab editor dengan split view dan IntelliSense
- 💻 **Terminal Terintegrasi**: Multi-tab terminal dengan xterm.js
- 🗃️ **SQLite Database**: Semua data tersimpan lokal tanpa internet
- 🔧 **Git Integration**: Git CLI terintegrasi dengan cache
- 🧩 **Extensions**: Support VSCode extensions
- 🌙 **Dark Mode**: GitHub style dengan glow components

## 🏗️ Struktur Monorepo

```
/apps/
  └─ studio/             → Next.js + Electron utama (KodeKilat Studio)
  └─ preview-server/     → Server lokal preview proyek

/packages/
  └─ editor/             → Monaco wrapper multi-tab
  └─ terminal/           → Terminal (xterm.js + node-pty)
  └─ kodekilatai/        → KodeKilat AI (multi-provider, SQLite)
  └─ ui-designer/        → Drag-and-drop UI builder
  └─ preview/            → Viewer readonly proyek
  └─ fs/                 → File System wrapper + SQLite
  └─ git/                → Git CLI + cache

/shared/
  └─ db/                 → SQLite handler via bun:sqlite
  └─ types/              → Tipe global
  └─ config/             → Tema, setting, dll
```

## 🛠️ Teknologi

- **Frontend**: Next.js App Router, TailwindCSS, Radix UI
- **Editor**: Monaco Editor
- **Terminal**: xterm.js + node-pty
- **Database**: SQLite via bun:sqlite
- **Runtime**: Bun + Node.js API
- **Desktop**: Electron

## 👨‍💻 Developer

**KangPCode (Dhafa Nazula P)**

## 📄 Lisensi

Creative Commons

## 🚀 Quick Start

```bash
# Clone repository
git clone <repository-url>
cd kodekilat

# Install dependencies
bun install

# Start development
bun dev
```

## 🎯 Roadmap

- [x] Setup Monorepo Structure
- [ ] Initialize Core Dependencies
- [ ] Setup SQLite Database Schema
- [ ] Create Electron Main Process
- [ ] Build Layout & Shell Foundation
- [ ] Implement Monaco Editor Package
- [ ] Create Terminal Package
- [ ] Build KodeKilat AI Package
- [ ] Implement File System Package
- [ ] Create UI Designer Package
- [ ] Build Git Integration Package
- [ ] Implement Project State Detection
- [ ] Create Settings & Theme System
- [ ] Build Extensions System
- [ ] Final Integration & Testing

---

**KodeKilat Studio ⚡** - Membuat coding jadi lebih kilat! 🇮🇩
