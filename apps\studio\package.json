{"name": "@kodekilat/studio", "version": "1.0.0", "description": "KodeKilat Studio - Main Application", "main": "electron/main.js", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start", "electron": "electron electron/main.js", "electron:dev": "concurrently \"bun run dev\" \"wait-on http://localhost:3000 && electron electron/main.js\"", "electron:build": "bun run build && electron-builder", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-button": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-tooltip": "^1.0.7", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "lucide-react": "^0.294.0", "@monaco-editor/react": "^4.6.0", "monaco-editor": "^0.44.0", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-web-links": "^0.9.0", "node-pty": "^1.0.0", "@kodekilat/db": "workspace:*", "@kodekilat/types": "workspace:*", "@kodekilat/config": "workspace:*", "@kodekilat/editor": "workspace:*", "@kodekilat/terminal": "workspace:*", "@kodekilat/kodekilatai": "workspace:*", "@kodekilat/ui-designer": "workspace:*", "@kodekilat/preview": "workspace:*", "@kodekilat/fs": "workspace:*", "@kodekilat/git": "workspace:*"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/node": "^20.10.0", "electron": "^27.1.3", "electron-builder": "^24.8.1", "concurrently": "^8.2.2", "wait-on": "^7.2.0", "typescript": "^5.3.2"}, "build": {"appId": "com.kangpcode.kodekilat-studio", "productName": "KodeKilat Studio", "directories": {"output": "dist"}, "files": ["out/**/*", "electron/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.developer-tools", "target": "dmg"}, "win": {"target": "nsis"}, "linux": {"target": ["AppImage", "deb"]}}}