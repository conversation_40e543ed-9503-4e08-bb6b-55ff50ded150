import type { 
  UserSettings, 
  ProjectSettings, 
  TerminalTheme, 
  AIProviderSettings,
  LayoutConfig,
  ResponsiveBreakpoints 
} from '@kodekilat/types';

// ============================================================================
// Default Configurations
// ============================================================================

export const DEFAULT_USER_SETTINGS: UserSettings = {
  theme: 'dark',
  fontSize: 14,
  fontFamily: 'JetBrains Mono, Consolas, Monaco, "Courier New", monospace',
  language: 'id',
  autoSave: true,
  autoSaveDelay: 1000,
  tabSize: 2,
  insertSpaces: true,
  wordWrap: 'on',
  minimap: true,
  lineNumbers: 'on',
  renderWhitespace: 'boundary'
};

export const DEFAULT_PROJECT_SETTINGS: ProjectSettings = {
  defaultPort: 3000,
  buildCommand: 'npm run build',
  devCommand: 'npm run dev',
  testCommand: 'npm test',
  lintCommand: 'npm run lint',
  formatCommand: 'npm run format',
  env: {},
  excludePatterns: [
    'node_modules/**',
    '.git/**',
    'dist/**',
    'build/**',
    '.next/**',
    '.nuxt/**',
    'coverage/**',
    '*.log',
    '.DS_Store',
    'Thumbs.db'
  ],
  includePatterns: [
    '**/*.{js,jsx,ts,tsx,vue,svelte,php,html,css,scss,sass,less,json,md,yml,yaml}'
  ]
};

export const DEFAULT_AI_SETTINGS: AIProviderSettings = {
  temperature: 0.7,
  maxTokens: 2048,
  topP: 1.0,
  frequencyPenalty: 0,
  presencePenalty: 0,
  timeout: 30000
};

// ============================================================================
// Theme Configurations
// ============================================================================

export const DEFAULT_TERMINAL_THEME: TerminalTheme = {
  background: '#1e1e1e',
  foreground: '#d4d4d4',
  cursor: '#ffffff',
  selection: '#264f78',
  black: '#000000',
  red: '#cd3131',
  green: '#0dbc79',
  yellow: '#e5e510',
  blue: '#2472c8',
  magenta: '#bc3fbc',
  cyan: '#11a8cd',
  white: '#e5e5e5',
  brightBlack: '#666666',
  brightRed: '#f14c4c',
  brightGreen: '#23d18b',
  brightYellow: '#f5f543',
  brightBlue: '#3b8eea',
  brightMagenta: '#d670d6',
  brightCyan: '#29b8db',
  brightWhite: '#ffffff'
};

export const DEFAULT_LAYOUT_CONFIG: LayoutConfig = {
  sidebar: {
    width: 280,
    collapsed: false,
    activePanel: 'files',
    panels: ['files', 'search', 'git', 'extensions', 'settings']
  },
  editor: {
    layout: {
      type: 'single',
      groups: []
    },
    minimap: true,
    wordWrap: true,
    lineNumbers: true,
    fontSize: 14,
    fontFamily: 'JetBrains Mono',
    theme: 'vs-dark'
  },
  terminal: {
    shell: process.platform === 'win32' ? 'cmd.exe' : '/bin/bash',
    fontSize: 14,
    fontFamily: 'JetBrains Mono',
    theme: DEFAULT_TERMINAL_THEME,
    scrollback: 1000,
    cursorBlink: true,
    cursorStyle: 'block'
  },
  ai: {
    width: 350,
    collapsed: false,
    provider: 'openai',
    model: 'gpt-4',
    temperature: 0.7,
    maxTokens: 2048
  },
  preview: {
    autoRefresh: true,
    refreshInterval: 1000,
    showDevTools: false,
    allowNavigation: true,
    customViewports: []
  }
};

export const DEFAULT_RESPONSIVE_BREAKPOINTS: ResponsiveBreakpoints = {
  mobile: 768,
  tablet: 1024,
  desktop: 1440
};



export const DARK_THEME = {
  name: 'KodeKilat Dark',
  colors: {
    // Base colors
    background: '#0d1117',
    foreground: '#e6edf3',
    
    // Surface colors
    surface: '#161b22',
    surfaceHover: '#21262d',
    surfaceActive: '#30363d',
    
    // Border colors
    border: '#30363d',
    borderHover: '#484f58',
    
    // Text colors
    textPrimary: '#e6edf3',
    textSecondary: '#7d8590',
    textMuted: '#656d76',
    
    // Accent colors
    accent: '#58a6ff',
    accentHover: '#79c0ff',
    accentActive: '#388bfd',
    
    // Status colors
    success: '#3fb950',
    warning: '#d29922',
    error: '#f85149',
    info: '#58a6ff',
    
    // Syntax highlighting
    syntax: {
      keyword: '#ff7b72',
      string: '#a5d6ff',
      comment: '#8b949e',
      number: '#79c0ff',
      function: '#d2a8ff',
      variable: '#ffa657',
      type: '#7ee787',
      operator: '#ff7b72',
      punctuation: '#e6edf3'
    },
    
    // Editor colors
    editor: {
      background: '#0d1117',
      lineHighlight: '#161b2240',
      selection: '#264f7840',
      cursor: '#e6edf3',
      gutter: '#21262d',
      gutterText: '#656d76'
    },
    
    // Terminal colors
    terminal: DEFAULT_TERMINAL_THEME
  }
};

export const LIGHT_THEME = {
  name: 'KodeKilat Light',
  colors: {
    // Base colors
    background: '#ffffff',
    foreground: '#24292f',
    
    // Surface colors
    surface: '#f6f8fa',
    surfaceHover: '#f3f4f6',
    surfaceActive: '#e5e7ea',
    
    // Border colors
    border: '#d0d7de',
    borderHover: '#bdc4cc',
    
    // Text colors
    textPrimary: '#24292f',
    textSecondary: '#656d76',
    textMuted: '#8c959f',
    
    // Accent colors
    accent: '#0969da',
    accentHover: '#0860ca',
    accentActive: '#0757ba',
    
    // Status colors
    success: '#1a7f37',
    warning: '#9a6700',
    error: '#cf222e',
    info: '#0969da',
    
    // Syntax highlighting
    syntax: {
      keyword: '#cf222e',
      string: '#0a3069',
      comment: '#6e7781',
      number: '#0550ae',
      function: '#8250df',
      variable: '#953800',
      type: '#116329',
      operator: '#cf222e',
      punctuation: '#24292f'
    },
    
    // Editor colors
    editor: {
      background: '#ffffff',
      lineHighlight: '#f6f8fa80',
      selection: '#0969da40',
      cursor: '#24292f',
      gutter: '#f6f8fa',
      gutterText: '#656d76'
    },
    
    // Terminal colors
    terminal: {
      background: '#ffffff',
      foreground: '#24292f',
      cursor: '#24292f',
      selection: '#0969da40',
      black: '#24292f',
      red: '#cf222e',
      green: '#116329',
      yellow: '#4d2d00',
      blue: '#0969da',
      magenta: '#8250df',
      cyan: '#1b7c83',
      white: '#6e7781',
      brightBlack: '#656d76',
      brightRed: '#a40e26',
      brightGreen: '#1a7f37',
      brightYellow: '#633c01',
      brightBlue: '#218bff',
      brightMagenta: '#a475f9',
      brightCyan: '#3192aa',
      brightWhite: '#8c959f'
    }
  }
};

// ============================================================================
// AI Provider Configurations
// ============================================================================

export const AI_PROVIDERS = {
  openai: {
    id: 'openai',
    name: 'OpenAI',
    baseUrl: 'https://api.openai.com/v1',
    models: [
      {
        id: 'gpt-4',
        name: 'GPT-4',
        maxTokens: 8192,
        supportsStreaming: true,
        supportsVision: false,
        costPer1kTokens: 0.03
      },
      {
        id: 'gpt-4-turbo',
        name: 'GPT-4 Turbo',
        maxTokens: 128000,
        supportsStreaming: true,
        supportsVision: true,
        costPer1kTokens: 0.01
      },
      {
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        maxTokens: 4096,
        supportsStreaming: true,
        supportsVision: false,
        costPer1kTokens: 0.002
      }
    ]
  },
  
  anthropic: {
    id: 'anthropic',
    name: 'Anthropic',
    baseUrl: 'https://api.anthropic.com/v1',
    models: [
      {
        id: 'claude-3-opus',
        name: 'Claude 3 Opus',
        maxTokens: 200000,
        supportsStreaming: true,
        supportsVision: true,
        costPer1kTokens: 0.015
      },
      {
        id: 'claude-3-sonnet',
        name: 'Claude 3 Sonnet',
        maxTokens: 200000,
        supportsStreaming: true,
        supportsVision: true,
        costPer1kTokens: 0.003
      },
      {
        id: 'claude-3-haiku',
        name: 'Claude 3 Haiku',
        maxTokens: 200000,
        supportsStreaming: true,
        supportsVision: true,
        costPer1kTokens: 0.00025
      }
    ]
  },
  
  huggingface: {
    id: 'huggingface',
    name: 'Hugging Face',
    baseUrl: 'https://api-inference.huggingface.co/models',
    models: [
      {
        id: 'microsoft/DialoGPT-large',
        name: 'DialoGPT Large',
        maxTokens: 1024,
        supportsStreaming: false,
        supportsVision: false,
        costPer1kTokens: 0
      },
      {
        id: 'microsoft/CodeBERT-base',
        name: 'CodeBERT',
        maxTokens: 512,
        supportsStreaming: false,
        supportsVision: false,
        costPer1kTokens: 0
      }
    ]
  },
  
  local: {
    id: 'local',
    name: 'Local Model',
    baseUrl: 'http://localhost:11434/v1',
    models: [
      {
        id: 'llama2',
        name: 'Llama 2',
        maxTokens: 4096,
        supportsStreaming: true,
        supportsVision: false,
        costPer1kTokens: 0
      },
      {
        id: 'codellama',
        name: 'Code Llama',
        maxTokens: 4096,
        supportsStreaming: true,
        supportsVision: false,
        costPer1kTokens: 0
      }
    ]
  }
};

// ============================================================================
// File Type Configurations
// ============================================================================

export const FILE_ICONS = {
  // Folders
  folder: '📁',
  folderOpen: '📂',
  
  // Programming languages
  javascript: '🟨',
  typescript: '🔷',
  python: '🐍',
  java: '☕',
  csharp: '🔷',
  cpp: '⚙️',
  c: '⚙️',
  rust: '🦀',
  go: '🐹',
  php: '🐘',
  ruby: '💎',
  swift: '🦉',
  kotlin: '🟣',
  scala: '🔴',
  
  // Web technologies
  html: '🌐',
  css: '🎨',
  scss: '🎨',
  sass: '🎨',
  less: '🎨',
  vue: '💚',
  react: '⚛️',
  angular: '🅰️',
  svelte: '🧡',
  
  // Data formats
  json: '📋',
  xml: '📄',
  yaml: '📝',
  yml: '📝',
  toml: '📝',
  csv: '📊',
  
  // Documentation
  markdown: '📝',
  txt: '📄',
  pdf: '📕',
  doc: '📘',
  docx: '📘',
  
  // Images
  png: '🖼️',
  jpg: '🖼️',
  jpeg: '🖼️',
  gif: '🖼️',
  svg: '🎨',
  ico: '🖼️',
  
  // Audio/Video
  mp3: '🎵',
  mp4: '🎬',
  avi: '🎬',
  mov: '🎬',
  
  // Archives
  zip: '📦',
  rar: '📦',
  tar: '📦',
  gz: '📦',
  
  // Config files
  gitignore: '🚫',
  env: '🔐',
  config: '⚙️',
  
  // Default
  default: '📄'
};

export const LANGUAGE_MAPPINGS = {
  '.js': 'javascript',
  '.jsx': 'javascript',
  '.ts': 'typescript',
  '.tsx': 'typescript',
  '.py': 'python',
  '.java': 'java',
  '.cs': 'csharp',
  '.cpp': 'cpp',
  '.c': 'c',
  '.rs': 'rust',
  '.go': 'go',
  '.php': 'php',
  '.rb': 'ruby',
  '.swift': 'swift',
  '.kt': 'kotlin',
  '.scala': 'scala',
  '.html': 'html',
  '.css': 'css',
  '.scss': 'scss',
  '.sass': 'sass',
  '.less': 'less',
  '.vue': 'vue',
  '.svelte': 'svelte',
  '.json': 'json',
  '.xml': 'xml',
  '.yaml': 'yaml',
  '.yml': 'yaml',
  '.toml': 'toml',
  '.md': 'markdown',
  '.txt': 'plaintext',
  '.sh': 'shell',
  '.bash': 'shell',
  '.zsh': 'shell',
  '.fish': 'shell',
  '.ps1': 'powershell',
  '.sql': 'sql',
  '.dockerfile': 'dockerfile',
  '.gitignore': 'ignore',
  '.env': 'dotenv'
};

// ============================================================================
// Keyboard Shortcuts
// ============================================================================

export const DEFAULT_KEYBINDINGS = {
  // File operations
  'file.new': 'Ctrl+N',
  'file.open': 'Ctrl+O',
  'file.save': 'Ctrl+S',
  'file.saveAs': 'Ctrl+Shift+S',
  'file.close': 'Ctrl+W',
  'file.closeAll': 'Ctrl+Shift+W',
  
  // Edit operations
  'edit.undo': 'Ctrl+Z',
  'edit.redo': 'Ctrl+Y',
  'edit.cut': 'Ctrl+X',
  'edit.copy': 'Ctrl+C',
  'edit.paste': 'Ctrl+V',
  'edit.selectAll': 'Ctrl+A',
  'edit.find': 'Ctrl+F',
  'edit.replace': 'Ctrl+H',
  'edit.findInFiles': 'Ctrl+Shift+F',
  
  // View operations
  'view.toggleSidebar': 'Ctrl+B',
  'view.toggleTerminal': 'Ctrl+`',
  'view.toggleAI': 'Ctrl+Shift+A',
  'view.zoomIn': 'Ctrl+=',
  'view.zoomOut': 'Ctrl+-',
  'view.zoomReset': 'Ctrl+0',
  
  // Navigation
  'nav.goToFile': 'Ctrl+P',
  'nav.goToSymbol': 'Ctrl+Shift+O',
  'nav.goToLine': 'Ctrl+G',
  'nav.nextTab': 'Ctrl+Tab',
  'nav.prevTab': 'Ctrl+Shift+Tab',
  
  // Terminal
  'terminal.new': 'Ctrl+Shift+`',
  'terminal.clear': 'Ctrl+K',
  
  // AI
  'ai.newChat': 'Ctrl+Shift+N',
  'ai.sendMessage': 'Ctrl+Enter',
  
  // Debug
  'debug.start': 'F5',
  'debug.stop': 'Shift+F5',
  'debug.restart': 'Ctrl+Shift+F5',
  'debug.stepOver': 'F10',
  'debug.stepInto': 'F11',
  'debug.stepOut': 'Shift+F11',
  'debug.toggleBreakpoint': 'F9'
};

// Export all configurations
export {
  DARK_THEME as defaultDarkTheme,
  LIGHT_THEME as defaultLightTheme
};
