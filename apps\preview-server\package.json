{"name": "@kodekilat/preview-server", "version": "1.0.0", "description": "KodeKilat Studio - Preview Server", "main": "index.js", "private": true, "scripts": {"dev": "bun run src/index.ts", "build": "bun build src/index.ts --outdir dist --target node", "start": "node dist/index.js", "type-check": "tsc --noEmit"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "ws": "^8.14.2", "chokidar": "^3.5.3", "@kodekilat/db": "workspace:*", "@kodekilat/types": "workspace:*"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/ws": "^8.5.10", "@types/node": "^20.10.0", "typescript": "^5.3.2"}}