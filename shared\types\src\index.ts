// ============================================================================
// KodeKilat Studio - Global Types
// ============================================================================

// User & Authentication Types
export interface User {
  id: number;
  username: string;
  email?: string;
  password_hash: string;
  avatar_url?: string;
  settings: UserSettings;
  created_at: string;
  updated_at: string;
}

export interface UserSettings {
  theme: 'dark' | 'light' | 'auto';
  fontSize: number;
  fontFamily: string;
  language: string;
  autoSave: boolean;
  autoSaveDelay: number;
  tabSize: number;
  insertSpaces: boolean;
  wordWrap: 'on' | 'off' | 'wordWrapColumn';
  minimap: boolean;
  lineNumbers: 'on' | 'off' | 'relative';
  renderWhitespace: 'none' | 'boundary' | 'selection' | 'all';
}

export interface Session {
  id: string;
  user_id: number;
  expires_at: string;
  data: Record<string, any>;
  created_at: string;
}

// Project Types
export interface Project {
  id: number;
  name: string;
  path: string;
  framework?: string;
  port?: number;
  is_running: boolean;
  last_opened: string;
  settings: ProjectSettings;
  created_at: string;
  updated_at: string;
}

export interface ProjectSettings {
  defaultPort?: number;
  buildCommand?: string;
  devCommand?: string;
  testCommand?: string;
  lintCommand?: string;
  formatCommand?: string;
  env?: Record<string, string>;
  excludePatterns?: string[];
  includePatterns?: string[];
}

export interface ProjectState {
  running: boolean;
  port: number | null;
  framework: string | null;
  path: string | null;
  pid?: number;
  startTime?: Date;
}

// Editor Types
export interface EditorState {
  id: number;
  project_id?: number;
  file_path: string;
  content?: string;
  cursor_position: CursorPosition;
  scroll_position: number;
  is_dirty: boolean;
  last_saved?: string;
  created_at: string;
  updated_at: string;
}

export interface CursorPosition {
  line: number;
  column: number;
}

export interface EditorTab {
  id: string;
  filePath: string;
  fileName: string;
  content: string;
  isDirty: boolean;
  isActive: boolean;
  language: string;
  cursorPosition: CursorPosition;
  scrollPosition: number;
}

export interface EditorLayout {
  type: 'single' | 'split-horizontal' | 'split-vertical';
  groups: EditorGroup[];
}

export interface EditorGroup {
  id: string;
  tabs: EditorTab[];
  activeTabId: string;
  size?: number;
}

// AI Types
export interface AISession {
  id: string;
  project_id?: number;
  title: string;
  provider: string;
  model: string;
  mode: 'chat' | 'auto' | 'thinking';
  context: AIContext;
  created_at: string;
  updated_at: string;
}

export interface AIMessage {
  id: number;
  session_id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  metadata: AIMessageMetadata;
  created_at: string;
}

export interface AIMessageMetadata {
  tokens?: number;
  model?: string;
  temperature?: number;
  attachments?: string[];
  codeBlocks?: CodeBlock[];
  thinking?: string;
}

export interface AIContext {
  files?: string[];
  codeSelection?: CodeSelection;
  terminalOutput?: string;
  gitStatus?: GitStatus;
  projectInfo?: ProjectInfo;
}

export interface AIProvider {
  id: string;
  name: string;
  baseUrl?: string;
  apiKey?: string;
  models: AIModel[];
  isActive: boolean;
  settings: AIProviderSettings;
}

export interface AIModel {
  id: string;
  name: string;
  maxTokens: number;
  supportsStreaming: boolean;
  supportsVision: boolean;
  costPer1kTokens?: number;
}

export interface AIProviderSettings {
  temperature: number;
  maxTokens: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  timeout?: number;
}

// Terminal Types
export interface TerminalSession {
  id: string;
  project_id?: number;
  name: string;
  cwd: string;
  shell: string;
  is_active: boolean;
  created_at: string;
}

export interface TerminalLog {
  id: number;
  session_id: string;
  type: 'input' | 'output' | 'error';
  content: string;
  timestamp: string;
}

export interface TerminalConfig {
  shell: string;
  fontSize: number;
  fontFamily: string;
  theme: TerminalTheme;
  scrollback: number;
  cursorBlink: boolean;
  cursorStyle: 'block' | 'underline' | 'bar';
}

export interface TerminalTheme {
  background: string;
  foreground: string;
  cursor: string;
  selection: string;
  black: string;
  red: string;
  green: string;
  yellow: string;
  blue: string;
  magenta: string;
  cyan: string;
  white: string;
  brightBlack: string;
  brightRed: string;
  brightGreen: string;
  brightYellow: string;
  brightBlue: string;
  brightMagenta: string;
  brightCyan: string;
  brightWhite: string;
}

// Git Types
export interface GitState {
  id: number;
  project_id: number;
  branch: string;
  status: GitStatus;
  last_commit?: string;
  remote_url?: string;
  is_dirty: boolean;
  updated_at: string;
}

export interface GitStatus {
  ahead: number;
  behind: number;
  staged: GitFileStatus[];
  unstaged: GitFileStatus[];
  untracked: string[];
  conflicted: string[];
}

export interface GitFileStatus {
  path: string;
  status: 'added' | 'modified' | 'deleted' | 'renamed' | 'copied';
  oldPath?: string;
}

export interface GitLog {
  id: number;
  project_id: number;
  command: string;
  output?: string;
  error?: string;
  exit_code: number;
  timestamp: string;
}

export interface GitCommit {
  hash: string;
  author: string;
  email: string;
  date: string;
  message: string;
  files: GitFileStatus[];
}

// File System Types
export interface FileSystemItem {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size?: number;
  modified?: string;
  children?: FileSystemItem[];
  isExpanded?: boolean;
  icon?: string;
}

export interface FileSystemCache {
  id: number;
  project_id?: number;
  path: string;
  type: 'file' | 'directory';
  size?: number;
  modified_at?: string;
  metadata: FileMetadata;
  cached_at: string;
}

export interface FileMetadata {
  encoding?: string;
  language?: string;
  lineCount?: number;
  permissions?: string;
  isSymlink?: boolean;
  target?: string;
}

// Extension Types
export interface Extension {
  id: string;
  name: string;
  version: string;
  author?: string;
  description?: string;
  is_enabled: boolean;
  settings: ExtensionSettings;
  install_path: string;
  installed_at: string;
  updated_at: string;
}

export interface ExtensionSettings {
  [key: string]: any;
}

export interface ExtensionManifest {
  name: string;
  displayName: string;
  description: string;
  version: string;
  author: string;
  license?: string;
  repository?: string;
  main: string;
  contributes?: ExtensionContributes;
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
}

export interface ExtensionContributes {
  commands?: ExtensionCommand[];
  keybindings?: ExtensionKeybinding[];
  languages?: ExtensionLanguage[];
  themes?: ExtensionTheme[];
  snippets?: ExtensionSnippet[];
  grammars?: ExtensionGrammar[];
}

export interface ExtensionCommand {
  command: string;
  title: string;
  category?: string;
  icon?: string;
}

export interface ExtensionKeybinding {
  command: string;
  key: string;
  when?: string;
}

export interface ExtensionLanguage {
  id: string;
  aliases: string[];
  extensions: string[];
  configuration?: string;
}

export interface ExtensionTheme {
  label: string;
  uiTheme: 'vs' | 'vs-dark' | 'hc-black';
  path: string;
}

export interface ExtensionSnippet {
  language: string;
  path: string;
}

export interface ExtensionGrammar {
  language: string;
  scopeName: string;
  path: string;
}

// UI Designer Types
export interface DesignerSession {
  id: string;
  project_id: number;
  file_path: string;
  components: UIComponent[];
  layout: DesignerLayout;
  responsive_breakpoints: ResponsiveBreakpoints;
  created_at: string;
  updated_at: string;
}

export interface UIComponent {
  id: string;
  type: string;
  name: string;
  props: Record<string, any>;
  children: UIComponent[];
  position: ComponentPosition;
  styles: ComponentStyles;
  responsive: ResponsiveStyles;
}

export interface ComponentPosition {
  x: number;
  y: number;
  width: number;
  height: number;
  zIndex: number;
}

export interface ComponentStyles {
  [property: string]: string | number;
}

export interface ResponsiveStyles {
  desktop: ComponentStyles;
  tablet: ComponentStyles;
  mobile: ComponentStyles;
}

export interface DesignerLayout {
  canvas: CanvasSettings;
  panels: PanelSettings;
  grid: GridSettings;
}

export interface CanvasSettings {
  width: number;
  height: number;
  zoom: number;
  background: string;
  showGrid: boolean;
  showRulers: boolean;
}

export interface PanelSettings {
  components: boolean;
  properties: boolean;
  layers: boolean;
  assets: boolean;
}

export interface GridSettings {
  size: number;
  color: string;
  opacity: number;
  snap: boolean;
}

export interface ResponsiveBreakpoints {
  mobile: number;
  tablet: number;
  desktop: number;
}

// Preview Types
export interface PreviewState {
  id: number;
  project_id: number;
  viewport: 'desktop' | 'tablet' | 'mobile' | 'custom';
  zoom_level: number;
  url?: string;
  is_responsive: boolean;
  breakpoint_settings: ResponsiveBreakpoints;
  updated_at: string;
}

export interface PreviewConfig {
  autoRefresh: boolean;
  refreshInterval: number;
  showDevTools: boolean;
  allowNavigation: boolean;
  customViewports: CustomViewport[];
}

export interface CustomViewport {
  name: string;
  width: number;
  height: number;
  devicePixelRatio: number;
  userAgent?: string;
}

// Common Utility Types
export interface CodeBlock {
  language: string;
  code: string;
  filename?: string;
  startLine?: number;
  endLine?: number;
}

export interface CodeSelection {
  filePath: string;
  startLine: number;
  endLine: number;
  startColumn: number;
  endColumn: number;
  selectedText: string;
}

export interface ProjectInfo {
  name: string;
  path: string;
  framework?: string;
  packageManager?: string;
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
  scripts?: Record<string, string>;
}

export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  duration?: number;
  actions?: NotificationAction[];
  timestamp: Date;
}

export interface NotificationAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary' | 'danger';
}

// Layout Types
export interface LayoutConfig {
  sidebar: SidebarConfig;
  editor: EditorConfig;
  terminal: TerminalConfig;
  ai: AIConfig;
  preview: PreviewConfig;
}

export interface SidebarConfig {
  width: number;
  collapsed: boolean;
  activePanel: string;
  panels: string[];
}

export interface EditorConfig {
  layout: EditorLayout;
  minimap: boolean;
  wordWrap: boolean;
  lineNumbers: boolean;
  fontSize: number;
  fontFamily: string;
  theme: string;
}

export interface AIConfig {
  width: number;
  collapsed: boolean;
  provider: string;
  model: string;
  temperature: number;
  maxTokens: number;
}

// Event Types
export interface AppEvent {
  type: string;
  payload: any;
  timestamp: Date;
}

export interface FileEvent extends AppEvent {
  type: 'file:open' | 'file:save' | 'file:close' | 'file:create' | 'file:delete' | 'file:rename';
  payload: {
    path: string;
    content?: string;
    oldPath?: string;
    newPath?: string;
  };
}

export interface ProjectEvent extends AppEvent {
  type: 'project:open' | 'project:close' | 'project:start' | 'project:stop';
  payload: {
    project: Project;
    port?: number;
  };
}

export interface TerminalEvent extends AppEvent {
  type: 'terminal:create' | 'terminal:close' | 'terminal:input' | 'terminal:output';
  payload: {
    sessionId: string;
    content?: string;
  };
}

export interface AIEvent extends AppEvent {
  type: 'ai:message' | 'ai:response' | 'ai:error' | 'ai:session:create' | 'ai:session:close';
  payload: {
    sessionId: string;
    message?: AIMessage;
    error?: string;
  };
}

// IPC Types (for Electron communication)
export interface IPCMessage {
  channel: string;
  data: any;
}

export interface IPCResponse {
  success: boolean;
  data?: any;
  error?: string;
}

// Export all types - removed circular reference
