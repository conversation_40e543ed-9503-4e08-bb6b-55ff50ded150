{"name": "kodekilat-studio", "version": "1.0.0", "description": "IDE Nusantara Modern - Cepat ⚡, Lokal, Tanpa Internet, Tidak Ribet", "main": "apps/studio/electron/main.js", "author": "KangPCode (D<PERSON>fa <PERSON>)", "license": "CC-BY-SA-4.0", "private": true, "workspaces": ["apps/*", "packages/*", "shared/*"], "scripts": {"dev": "concurrently \"bun run dev:studio\" \"bun run dev:preview\"", "dev:studio": "cd apps/studio && bun run dev", "dev:preview": "cd apps/preview-server && bun run dev", "build": "bun run build:packages && bun run build:studio", "build:packages": "bun run build:shared && bun run build:packages:all", "build:shared": "cd shared/db && bun run build && cd ../types && bun run build && cd ../config && bun run build", "build:packages:all": "cd packages/editor && bun run build && cd ../terminal && bun run build && cd ../kodekilatai && bun run build && cd ../ui-designer && bun run build && cd ../preview && bun run build && cd ../fs && bun run build && cd ../git && bun run build", "build:studio": "cd apps/studio && bun run build", "electron": "cd apps/studio && bun run electron", "electron:dev": "cd apps/studio && bun run electron:dev", "electron:build": "cd apps/studio && bun run electron:build", "test": "bun test", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "type-check": "tsc --noEmit", "clean": "rimraf node_modules apps/*/node_modules packages/*/node_modules shared/*/node_modules", "install:all": "bun install && bun run install:workspaces", "install:workspaces": "cd apps/studio && bun install && cd ../preview-server && bun install"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "concurrently": "^8.2.2", "eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "husky": "^8.0.3", "prettier": "^3.1.0", "rimraf": "^5.0.5", "typescript": "^5.3.2"}, "dependencies": {"electron": "^27.1.3", "electron-builder": "^24.8.1", "electron-is-dev": "^2.0.0"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}, "keywords": ["ide", "editor", "electron", "nextjs", "monaco", "ai", "indonesia", "nusantara", "offline", "sqlite"], "repository": {"type": "git", "url": "https://github.com/kangpcode/kodekilat-studio.git"}, "bugs": {"url": "https://github.com/kangpcode/kodekilat-studio/issues"}, "homepage": "https://github.com/kangpcode/kodekilat-studio#readme"}