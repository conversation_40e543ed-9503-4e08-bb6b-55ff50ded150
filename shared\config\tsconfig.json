{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "declaration": true, "declarationMap": true, "sourceMap": true, "target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@kodekilat/types": ["../types/src"]}}, "include": ["src/**/*"], "exclude": ["dist", "node_modules"]}