declare module "bun:sqlite" {
  export interface DatabaseOptions {
    readonly?: boolean;
    create?: boolean;
    readwrite?: boolean;
  }

  export interface PreparedStatement {
    run(...params: any[]): { changes: number; lastInsertRowid: number };
    get(...params: any[]): any;
    all(...params: any[]): any[];
    finalize(): void;
  }

  export class Database {
    constructor(path: string, options?: DatabaseOptions);
    
    exec(sql: string): void;
    prepare(sql: string): PreparedStatement;
    close(): void;
    backup(destination: Database): void;
    
    readonly filename: string;
    readonly inTransaction: boolean;
  }
}
