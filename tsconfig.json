{"compilerOptions": {"target": "ES2022", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./apps/studio/src/*"], "@kodekilat/db": ["./shared/db/src"], "@kodekilat/types": ["./shared/types/src"], "@kodekilat/config": ["./shared/config/src"], "@kodekilat/editor": ["./packages/editor/src"], "@kodekilat/terminal": ["./packages/terminal/src"], "@kodekilat/kodekilatai": ["./packages/kodekilatai/src"], "@kodekilat/ui-designer": ["./packages/ui-designer/src"], "@kodekilat/preview": ["./packages/preview/src"], "@kodekilat/fs": ["./packages/fs/src"], "@kodekilat/git": ["./packages/git/src"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "dist", "out", ".next"]}